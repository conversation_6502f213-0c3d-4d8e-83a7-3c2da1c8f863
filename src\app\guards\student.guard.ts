import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, map, catchError, take, timeout } from 'rxjs';
import { of } from 'rxjs';
import { StudentAuthService } from '../services/student-auth.service';

@Injectable({
  providedIn: 'root'
})
export class StudentGuard implements CanActivate {

  constructor(
    private studentAuthService: StudentAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    console.log('🔐 StudentGuard: Checking student access for route:', state.url);
    console.log('🔍 Current student auth state:', this.studentAuthService.isAuthenticated());
    console.log('👤 Current student:', this.studentAuthService.getCurrentStudent());

    // Force refresh authentication state from localStorage first
    this.studentAuthService.refreshAuthState();

    // Check if already authenticated after refresh
    if (this.studentAuthService.isAuthenticated()) {
      const student = this.studentAuthService.getCurrentStudent();
      console.log('👤 Current student from guard after refresh:', student);

      if (student) {
        console.log(`✅ Student access granted for: ${student.fullName} (${student.studentId})`);
        return true;
      } else {
        console.log('❌ No current student found despite authentication');
        this.redirectToLogin(state.url);
        return false;
      }
    }

    // If not immediately authenticated, validate session with backend
    console.log('🔍 Not immediately authenticated, validating session...');
    return this.studentAuthService.validateSession().pipe(
      timeout(10000), // 10 second timeout
      take(1), // Take only the first emission
      map((isValid: boolean) => {
        console.log('✅ Student session validation result:', isValid);

        if (isValid) {
          // Double-check authentication state after validation
          if (this.studentAuthService.isAuthenticated()) {
            const student = this.studentAuthService.getCurrentStudent();
            console.log('👤 Current student from guard after validation:', student);

            if (student) {
              console.log(`✅ Student access granted after validation for: ${student.fullName} (${student.studentId})`);
              return true;
            } else {
              console.log('❌ No current student found after validation');
            }
          } else {
            console.log('❌ Authentication state not updated after validation');
          }
        } else {
          console.log('❌ Student session invalid or not authenticated');
        }

        this.redirectToLogin(state.url);
        return false;
      }),
      catchError((error) => {
        console.error('❌ Error in StudentGuard:', error);
        this.redirectToLogin(state.url);
        return of(false);
      })
    );
  }

  private redirectToLogin(returnUrl: string): void {
    console.log('❌ Student access denied - redirecting to login');
    this.router.navigate(['/login'], {
      queryParams: { returnUrl }
    });
  }
}
