import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';

interface Student {
  studentId: string;
  fullName: string;
  course: string;
  yearLevel: string;
  section: string;
  email: string;
  phoneNumber?: string;
  status: string;
}

@Injectable({
  providedIn: 'root'
})
export class StudentAuthService {
  private currentStudentSubject = new BehaviorSubject<Student | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentStudent$ = this.currentStudentSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(private apiService: ApiService) {
    // Initialize as not authenticated by default
    this.isAuthenticatedSubject.next(false);
    this.currentStudentSubject.next(null);

    // Then check for stored authentication
    this.checkStoredAuth();
  }

  /**
   * Check if there's stored authentication data
   */
  private checkStoredAuth(): void {
    const storedStudent = localStorage.getItem('currentStudent');
    const storedToken = localStorage.getItem('studentToken');
    const loginTimestamp = localStorage.getItem('studentLoginTimestamp');

    if (storedStudent && storedToken && loginTimestamp) {
      try {
        const student = JSON.parse(storedStudent);
        const timestamp = parseInt(loginTimestamp);
        const now = Date.now();
        const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        // Check if session is still valid (within 24 hours)
        if (now - timestamp < sessionDuration) {
          this.currentStudentSubject.next(student);
          this.isAuthenticatedSubject.next(true);
          console.log('🔄 Restored student authentication from localStorage:', student.fullName);
        } else {
          console.log('🕐 Student session expired, clearing stored auth');
          this.clearStoredAuth();
        }
      } catch (error) {
        console.error('Error parsing stored student data:', error);
        this.clearStoredAuth();
      }
    } else {
      // Clear any incomplete stored data
      this.clearStoredAuth();
    }
  }

  /**
   * Force refresh authentication state from localStorage
   */
  refreshAuthState(): void {
    console.log('🔄 Forcing student authentication state refresh...');
    this.checkStoredAuth();
  }

  /**
   * Clear session and force logout
   */
  clearSession(): void {
    console.log('🧹 Manually clearing student session...');
    this.logout();
  }

  /**
   * Student login method
   */
  studentLogin(studentId: string, password: string): Observable<boolean> {
    console.log('🚀 Starting student login for:', studentId);
    console.log('🔍 Current auth state before login:', this.isAuthenticated());
    console.log('👤 Current student before login:', this.getCurrentStudent());

    return this.apiService.login({ studentId, password }).pipe(
      tap((response: any) => {
        console.log('🔍 Raw API Response:', response);
      }),
      map((response: any) => {
        console.log('🔍 Processing API Response:', response);

        if (response && response.success && response.data) {
          const student: Student = {
            studentId: response.data.StudentID,
            fullName: response.data.FullName,
            course: response.data.Course,
            yearLevel: response.data.YearLevel,
            section: response.data.Section,
            email: response.data.Email,
            phoneNumber: response.data.PhoneNumber,
            status: response.data.Status
          };

          console.log('✅ Student data processed:', student);

          // Store in localStorage first (this is synchronous)
          localStorage.setItem('currentStudent', JSON.stringify(student));
          // Store the actual JWT token from the backend response
          if (response.token) {
            localStorage.setItem('studentToken', response.token);
            console.log('🔑 JWT token stored:', response.token.substring(0, 20) + '...');
          } else {
            console.warn('⚠️ No token received from backend, using fallback');
            localStorage.setItem('studentToken', 'student-token-' + Date.now());
          }
          localStorage.setItem('studentLoginTimestamp', Date.now().toString());

          // Then update the subjects (this triggers observables)
          this.currentStudentSubject.next(student);
          this.isAuthenticatedSubject.next(true);

          console.log('✅ Student login successful, authentication state updated');
          console.log('🔐 Current auth state:', this.isAuthenticated());
          console.log('👤 Current student:', this.getCurrentStudent());
          console.log('💾 LocalStorage student:', localStorage.getItem('currentStudent'));
          console.log('💾 LocalStorage token:', localStorage.getItem('studentToken'));

          return true;
        } else {
          console.error('❌ Student login failed - invalid response structure');
          console.error('Response:', response);
          return false;
        }
      }),
      catchError((error) => {
        console.error('❌ Student login error:', error);
        return of(false);
      })
    );
  }

  /**
   * Get current student
   */
  getCurrentStudent(): Student | null {
    return this.currentStudentSubject.value;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem('studentToken');
  }

  /**
   * Logout method
   */
  logout(): void {
    console.log('🚪 Student logging out...');
    this.clearStoredAuth();
    this.currentStudentSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    console.log('✅ Student logout complete');
  }

  /**
   * Clear stored authentication data
   */
  private clearStoredAuth(): void {
    localStorage.removeItem('currentStudent');
    localStorage.removeItem('studentToken');
    localStorage.removeItem('studentLoginTimestamp');
  }

  /**
   * Validate student session with backend
   */
  validateSession(): Observable<boolean> {
    const student = this.getCurrentStudent();
    const token = this.getToken();
    const loginTimestamp = localStorage.getItem('studentLoginTimestamp');

    if (!student || !token || !loginTimestamp) {
      console.log('❌ Session validation failed: Missing required data');
      this.logout();
      return of(false);
    }

    // First check local timestamp for basic validation
    try {
      const timestamp = parseInt(loginTimestamp);
      const now = Date.now();
      const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (now - timestamp >= sessionDuration) {
        console.log('❌ Session validation failed: Local session expired');
        this.logout();
        return of(false);
      }
    } catch (error) {
      console.error('❌ Session validation failed: Error parsing timestamp', error);
      this.logout();
      return of(false);
    }

    // If local validation passes, validate with backend
    console.log('🔍 Validating session with backend...');
    return this.apiService.validateStudentSession(token).pipe(
      map((response: any) => {
        if (response && response.success && response.data) {
          console.log('✅ Backend session validation successful');

          // Update stored student data with fresh data from backend
          const updatedStudent: Student = {
            studentId: response.data.StudentID,
            fullName: response.data.FullName,
            course: response.data.Course,
            yearLevel: response.data.YearLevel,
            section: response.data.Section,
            email: response.data.Email,
            phoneNumber: response.data.PhoneNumber,
            status: response.data.AccountStatus
          };

          localStorage.setItem('currentStudent', JSON.stringify(updatedStudent));
          this.currentStudentSubject.next(updatedStudent);

          return true;
        } else {
          console.log('❌ Backend session validation failed');
          this.logout();
          return false;
        }
      }),
      catchError((error) => {
        console.error('❌ Session validation error:', error);
        this.logout();
        return of(false);
      })
    );
  }

  /**
   * Update student profile
   */
  updateProfile(updates: Partial<Student>): Observable<boolean> {
    const currentStudent = this.getCurrentStudent();
    if (!currentStudent) {
      return of(false);
    }

    const updatedStudent = { ...currentStudent, ...updates };
    
    // Update localStorage
    localStorage.setItem('currentStudent', JSON.stringify(updatedStudent));
    
    // Update subject
    this.currentStudentSubject.next(updatedStudent);
    
    console.log('✅ Student profile updated:', updatedStudent);
    return of(true);
  }

  /**
   * Check if student has specific permissions
   */
  hasPermission(permission: string): boolean {
    const student = this.getCurrentStudent();
    if (!student) return false;

    // Students have basic permissions by default
    const studentPermissions = [
      'view_books',
      'borrow_books',
      'view_loans',
      'renew_books',
      'make_reservations',
      'view_profile',
      'update_profile'
    ];

    return studentPermissions.includes(permission);
  }

  /**
   * Get student dashboard stats
   */
  getDashboardStats(): Observable<any> {
    // Mock data - replace with actual API call
    return of({
      totalBooksLoaned: 3,
      overdueBooks: 1,
      availableRenewals: 5,
      libraryFines: 5,
      reservations: 2,
      favoriteBooks: 12
    });
  }
}
